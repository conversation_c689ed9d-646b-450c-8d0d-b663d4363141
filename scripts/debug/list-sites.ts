#!/usr/bin/env ts-node

/**
 * Simple script to list all deployed Remotion sites
 * 
 * This is a lightweight version that only fetches deployed sites
 * without attempting to bundle the local project.
 * 
 * Usage:
 *   ts-node list-sites.ts
 *   node list-sites.js (if compiled)
 */

import { getSites, AwsRegion } from "@remotion/lambda/client";
import * as dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, ".env") });

// Simple config for AWS credentials
const awsConfig = {
  region: process.env.AWS_REGION || 'eu-west-1',
  accessKeyId: process.env.REMOTION_AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.REMOTION_AWS_SECRET_ACCESS_KEY,
};

// Set environment variables for Remotion
if (awsConfig.accessKeyId) process.env.REMOTION_AWS_ACCESS_KEY_ID = awsConfig.accessKeyId;
if (awsConfig.secretAccessKey) process.env.REMOTION_AWS_SECRET_ACCESS_KEY = awsConfig.secretAccessKey;
if (awsConfig.region) process.env.AWS_REGION = awsConfig.region;

// ANSI color codes for better output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text: string, color: keyof typeof colors): string {
  return `${colors[color]}${text}${colors.reset}`;
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function main(): Promise<void> {
  try {
    console.log(colorize('🚀 Fetching Remotion Sites...', 'cyan'));
    console.log(colorize('================================', 'cyan'));
    
    const region = awsConfig.region as AwsRegion;
    console.log(`Region: ${colorize(region, 'blue')}\n`);

    const sites = await getSites({
      region: region,
    });
    
    if (sites.sites.length === 0) {
      console.log(colorize('❌ No deployed sites found!', 'red'));
      console.log('\nTo deploy a site, run:');
      console.log(colorize('npm run deploy-remotion-dev', 'yellow'));
      console.log('or');
      console.log(colorize('npx remotion lambda sites create src/remotion/index.ts --site-name=boostcast-video --region=eu-west-1', 'yellow'));
      return;
    }
    
    console.log(colorize(`✅ Found ${sites.sites.length} deployed site(s):\n`, 'green'));
    
    sites.sites.forEach((site, index) => {
      console.log(colorize(`📦 Site ${index + 1}`, 'magenta'));
      console.log(colorize('─'.repeat(20), 'magenta'));
      console.log(`${colorize('ID:', 'blue')} ${site.id}`);
      console.log(`${colorize('URL:', 'blue')} ${site.serveUrl}`);
      console.log(`${colorize('Bucket:', 'blue')} ${site.bucketName}`);
      console.log(`${colorize('Size:', 'blue')} ${formatBytes(site.sizeInBytes)}`);

      if (site.lastModified) {
        // lastModified is already a timestamp in milliseconds or a Date object
        const lastModifiedDate = typeof site.lastModified === 'number'
          ? new Date(site.lastModified)
          : new Date(site.lastModified);
        console.log(`${colorize('Last Modified:', 'blue')} ${lastModifiedDate.toLocaleString()}`);
      }
      
      if (index < sites.sites.length - 1) {
        console.log(''); // Add spacing between sites
      }
    });
    
    console.log(colorize('\n✨ Done!', 'green'));
    
    // Show which site would be used by default
    if (sites.sites.length > 0) {
      console.log(colorize('\n💡 Info:', 'yellow'));
      console.log(`The rendering service will use: ${colorize(sites.sites[0].serveUrl, 'cyan')}`);
    }
    
  } catch (error) {
    console.error(colorize(`\n❌ Error: ${error.message}`, 'red'));
    
    if (error.message.includes('credentials')) {
      console.log(colorize('\n💡 Tip: Make sure your AWS credentials are properly configured:', 'yellow'));
      console.log('- Check your .env file has REMOTION_AWS_ACCESS_KEY_ID and REMOTION_AWS_SECRET_ACCESS_KEY');
      console.log('- Verify AWS_REGION is set correctly');
    }
    
    if (error.message.includes('region')) {
      console.log(colorize('\n💡 Tip: Check that AWS_REGION is set to a valid region (e.g., eu-west-1)', 'yellow'));
    }
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(colorize('Unhandled Rejection:', 'red'), reason);
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}
